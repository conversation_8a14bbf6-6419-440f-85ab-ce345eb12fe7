from typing import Any, Dict
from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>SO<PERSON>, <PERSON>olean, Column, Date, Float, String, Text, DateTime, ForeignKey, Integer, Index
from sqlalchemy.orm import relationship
from pgvector.sqlalchemy import Vector
from sqlalchemy.orm import declarative_base
from sqlalchemy.dialects.postgresql import JSONB
from sqlalchemy.sql import expression
from werkzeug.security import generate_password_hash, check_password_hash
from flask_login import UserMixin

Base = declarative_base()


# User Authentication Models
class User(Base, UserMixin):
    """User model for authentication and user management."""
    __tablename__ = 'users'

    id = Column(Integer, primary_key=True)
    username = Column(String(80), unique=True, nullable=False, index=True)
    email = Column(String(120), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)

    # Profile information
    first_name = Column(String(50))
    last_name = Column(String(50))

    # Account status
    is_active = Column(<PERSON>olean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False)
    updated_at = Column(DateTime(timezone=True), nullable=False)
    last_login = Column(DateTime(timezone=True))

    # Email preferences
    email_preferences = Column(JSONB, default=lambda: {
        'daily_summary': True,
        'market_alerts': True,
        'news_digest': True,
        'frequency': 'daily'  # daily, weekly, never
    })

    # User preferences
    user_preferences = Column(JSONB, default=lambda: {
        'theme': 'light',
        'timezone': 'UTC',
        'language': 'en',
        'news_sources': [],
        'watchlist': []
    })

    # Relationships
    email_logs = relationship(
        "EmailLog", back_populates="user", cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index('ix_users_email_active', 'email', 'is_active'),
        Index('ix_users_username_active', 'username', 'is_active'),
        Index('ix_users_created_at', 'created_at'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<User(username={self.username!r}, email={self.email!r})>"

    def set_password(self, password):
        """Set password hash."""
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        """Check password against hash."""
        return check_password_hash(self.password_hash, password)

    def get_id(self):
        """Return user ID as string for Flask-Login."""
        return str(self.id)

    def to_dict(self, include_sensitive=False):
        """Convert user to dictionary."""
        data = {
            'id': self.id,
            'username': self.username,
            'email': self.email,
            'first_name': self.first_name,
            'last_name': self.last_name,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'created_at': self.created_at,
            'updated_at': self.updated_at,
            'last_login': self.last_login,
            'email_preferences': self.email_preferences,
            'user_preferences': self.user_preferences
        }

        if include_sensitive:
            data['password_hash'] = self.password_hash

        return data


class EmailLog(Base):
    """Email log model for tracking sent emails."""
    __tablename__ = 'email_logs'

    id = Column(Integer, primary_key=True)
    user_id = Column(Integer, ForeignKey(
        'users.id', ondelete='CASCADE'), nullable=False)

    # Email details
    # daily_summary, market_alert, etc.
    email_type = Column(String(50), nullable=False)
    subject = Column(String(255), nullable=False)
    recipient_email = Column(String(120), nullable=False)

    # Status tracking
    status = Column(String(20), default='pending',
                    nullable=False)  # pending, sent, failed
    sent_at = Column(DateTime(timezone=True))
    error_message = Column(Text)

    # Content metadata
    content_metadata = Column(JSONB)  # Store summary of email content

    # Timestamps
    created_at = Column(DateTime(timezone=True), nullable=False)

    # Relationships
    user = relationship("User", back_populates="email_logs")

    # Indexes for performance
    __table_args__ = (
        Index('ix_email_logs_user_id', 'user_id'),
        Index('ix_email_logs_status', 'status'),
        Index('ix_email_logs_email_type', 'email_type'),
        Index('ix_email_logs_sent_at', 'sent_at'),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<EmailLog(user_id={self.user_id}, type={self.email_type}, status={self.status})>"


# postgresql models
class Article(Base):
    __tablename__ = "articles"

    id = Column(String, primary_key=True)
    title = Column(String, nullable=False)
    content = Column(Text)
    url = Column(String, nullable=False, unique=True)
    author = Column(String)
    source = Column(String)
    publish_time = Column(DateTime(timezone=True))
    update_time = Column(DateTime(timezone=True))
    # update time or publish time
    date = Column(DateTime(timezone=True), nullable=False)
    tags = Column(ARRAY(String))
    crawl_time = Column(DateTime(timezone=True))
    article_metadata = Column(JSONB)  # flexible metadata for filtering

    # Relationships
    chunks = relationship("Chunk", back_populates="article",
                          cascade="all, delete-orphan")

    # Indexes for performance
    __table_args__ = (
        Index("ix_articles_date", "date"),
        Index("ix_articles_source", "source"),
        Index("ix_articles_metadata_gin",
              "article_metadata", postgresql_using="gin"),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<Article(title={self.title[:30]!r}, date={self.date})>"

    def to_dict(self, include_chunks: bool = False) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "title": self.title,
            "content": self.content,
            "url": self.url,
            "author": self.author,
            "source": self.source,
            "publish_time": self.publish_time,
            "update_time": self.update_time,
            "date": self.date,
            "tags": self.tags,
            "crawl_time": self.crawl_time,
            "article_metadata": self.article_metadata,
        }

        if include_chunks and hasattr(self, "chunks"):
            data["chunks"] = [
                {
                    "id": chunk.id,
                    "text": chunk.text,
                    "embedding": chunk.embedding,
                    "chunk_index": chunk.chunk_index,
                    "model_name": chunk.model_name,
                    "chunk_metadata": chunk.chunk_metadata,
                    "article_id": chunk.article_id,
                }
                for chunk in self.chunks
            ]

        return data


class Chunk(Base):
    __tablename__ = "chunks"

    id = Column(String, primary_key=True)

    # source article info
    article_id = Column(String, ForeignKey(
        "articles.id", ondelete="CASCADE"), nullable=False)
    article_date = Column(DateTime(timezone=True), nullable=False)
    article_title = Column(String, nullable=False)
    article_url = Column(String, nullable=False)

    type = Column(String)
    chunk_index = Column(Integer, nullable=False)
    text = Column(Text, nullable=False)
    embedding = Column(Vector(768))
    model_name = Column(String)

    # per-chunk metadata (e.g., topic, sentiment)
    chunk_metadata = Column(JSONB)

    # Relationships
    article = relationship("Article", back_populates="chunks")

    # Indexes for performance
    __table_args__ = (
        Index("ix_chunks_article_url", "article_url"),
        Index("ix_chunks_article_date", "article_date"),
        Index("ix_chunks_article_title", "article_title"),
        Index(
            "ix_chunks_recent_date",
            "article_date",
            postgresql_where=Column(
                "article_date", DateTime) >= expression.literal("2024-01-01")
        ),
        Index("ix_chunks_date_title", "article_date", "article_title"),
        Index("ix_chunks_metadata_gin", "chunk_metadata", postgresql_using="gin"),
        {'extend_existing': True},
    )

    def __repr__(self):
        return f"<Chunk(article_url={self.article_url}, index={self.chunk_index})>"

    def to_dict(self) -> Dict[str, Any]:
        data = {
            "id": self.id,
            "type": self.type,
            "text": self.text,
            "article_id": self.article_id,
            "article_date": self.article_date,
            "article_title": self.article_title,
            "article_url": self.article_url,
            "embedding": self.embedding,
            "chunk_index": self.chunk_index,
            "model_name": self.model_name,
            "chunk_metadata": self.chunk_metadata,
        }

        return data


class MarketData(Base):
    """Market data model for storing historical price data."""
    __tablename__ = 'market_data'

    ticker = Column(String, primary_key=True)
    date = Column(Date, primary_key=True)
    interval = Column(String, primary_key=True)

    open_price = Column(Float)
    high_price = Column(Float)
    low_price = Column(Float)
    close_price = Column(Float)
    volume = Column(Integer)

    # Indexes for performance
    __table_args__ = (
        Index('ix_market_data_open_price', 'open_price'),
        {'extend_existing': True},
    )


class LlmApiResults(Base):
    """Results from LLM APIs."""
    __tablename__ = 'llm_api_results'

    id = Column(String, primary_key=True)
    article_id = Column(String)
    api = Column(String)
    model = Column(String)
    prompt_type = Column(String)
    prompt = Column(Text)
    batch_id = Column(String)
    status = Column(String)
    content = Column(JSONB)
    raw_response = Column(Text)
    cost = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)

    __table_args__ = (
        Index('ix_llm_result_status', 'status'),
        Index('ix_llm_result_api', 'api'),
        Index('ix_llm_result_article_id', 'article_id'),
        Index('ix_llm_result_model', 'model'),
        Index('ix_llm_result_prompt_type', 'prompt_type'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            "id": self.id,
            "article_id": self.article_id,
            "api": self.api,
            "model": self.model,
            "prompt_type": self.prompt_type,
            "prompt": self.prompt,
            "batch_id": self.batch_id,
            "status": self.status,
            "content": self.content,
            "raw_response": self.raw_response,
            "cost": self.cost,
            "input_tokens": self.input_tokens,
            "output_tokens": self.output_tokens
        }


class LlmApiBatches(Base):
    """Llm api batches tracking model."""
    __tablename__ = 'llm_api_batches'

    id = Column(String, primary_key=True)
    api = Column(String)
    prompt_type = Column(String)
    status = Column(String)
    created_at = Column(DateTime(timezone=True))
    completed_at = Column(DateTime(timezone=True))
    expires_at = Column(DateTime(timezone=True))
    raw_response = Column(Text)
    cost = Column(Float)
    input_tokens = Column(Integer)
    output_tokens = Column(Integer)

    # Indexes
    __table_args__ = (
        Index('ix_llm_batch_status', 'status'),
        Index('ix_llm_batch_api', 'api'),
        Index('ix_llm_batch_prompt_type', 'prompt_type'),
        {'extend_existing': True},
    )

    def to_dict(self) -> Dict[str, Any]:
        return {
            'id': self.id,
            'api': self.api,
            'status': self.status,
            'created_at': self.created_at,
            'completed_at': self.completed_at,
            'expires_at': self.expires_at,
            'prompt_type': self.prompt_type,
            'raw_response': self.raw_response,
            'cost': self.cost,
            'input_tokens': self.input_tokens,
            'output_tokens': self.output_tokens
        }

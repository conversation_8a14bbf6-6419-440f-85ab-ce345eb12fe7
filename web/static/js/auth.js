// Authentication JavaScript functionality

// Password visibility toggle
function togglePassword(fieldId) {
    const passwordField = document.getElementById(fieldId);
    const toggleIcon = document.getElementById(fieldId + '-toggle-icon');

    if (passwordField.type === 'password') {
        passwordField.type = 'text';
        toggleIcon.classList.remove('bi-eye');
        toggleIcon.classList.add('bi-eye-slash');
    } else {
        passwordField.type = 'password';
        toggleIcon.classList.remove('bi-eye-slash');
        toggleIcon.classList.add('bi-eye');
    }
}

// Password strength checker
function checkPasswordStrength(password) {
    let score = 0;
    let feedback = [];

    // Length check
    if (password.length >= 8) {
        score += 1;
    } else {
        feedback.push('At least 8 characters');
    }

    // Uppercase check
    if (/[A-Z]/.test(password)) {
        score += 1;
    } else {
        feedback.push('One uppercase letter');
    }

    // Lowercase check
    if (/[a-z]/.test(password)) {
        score += 1;
    } else {
        feedback.push('One lowercase letter');
    }

    // Number check
    if (/\d/.test(password)) {
        score += 1;
    } else {
        feedback.push('One number');
    }

    // Special character check
    if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
        score += 1;
    } else {
        feedback.push('One special character');
    }

    return { score, feedback };
}

// Update password strength meter
function updatePasswordStrength(password) {
    const strengthFill = document.getElementById('password-strength-fill');
    const strengthText = document.getElementById('password-strength-text');

    // Only proceed if both elements exist (they're only on registration page)
    if (!strengthFill || !strengthText) {
        console.log('Password strength meter elements not found - skipping update');
        return;
    }

    try {
        const { score, feedback } = checkPasswordStrength(password);

        // Remove all strength classes
        strengthFill.classList.remove('weak', 'fair', 'good', 'strong');

        if (password.length === 0) {
            strengthFill.style.width = '0%';
            strengthText.textContent = 'Password strength';
            strengthText.className = 'password-strength-text';
            return;
        }

        // Update strength meter based on score
        switch (score) {
            case 0:
            case 1:
                strengthFill.classList.add('weak');
                strengthText.textContent = 'Weak - Missing: ' + feedback.join(', ');
                strengthText.className = 'password-strength-text text-danger';
                break;
            case 2:
                strengthFill.classList.add('fair');
                strengthText.textContent = 'Fair - Missing: ' + feedback.join(', ');
                strengthText.className = 'password-strength-text text-warning';
                break;
            case 3:
            case 4:
                strengthFill.classList.add('good');
                strengthText.textContent = feedback.length > 0 ? 'Good - Missing: ' + feedback.join(', ') : 'Good password';
                strengthText.className = 'password-strength-text text-info';
                break;
            case 5:
                strengthFill.classList.add('strong');
                strengthText.textContent = 'Strong password';
                strengthText.className = 'password-strength-text text-success';
                break;
        }
    } catch (error) {
        console.error('Error updating password strength:', error);
    }
}

// Form validation
function validateForm(formId) {
    const form = document.getElementById(formId);
    if (!form) return true;

    let isValid = true;
    const inputs = form.querySelectorAll('input[required], select[required]');

    inputs.forEach(input => {
        if (!input.value.trim()) {
            input.classList.add('is-invalid');
            isValid = false;
        } else {
            input.classList.remove('is-invalid');
        }
    });

    // Email validation
    const emailInputs = form.querySelectorAll('input[type="email"]');
    emailInputs.forEach(input => {
        if (input.value && !isValidEmail(input.value)) {
            input.classList.add('is-invalid');
            isValid = false;
        }
    });

    // Password confirmation validation
    const passwordField = form.querySelector('input[name="password"]');
    const confirmField = form.querySelector('input[name="password_confirm"]');

    if (passwordField && confirmField && passwordField.value !== confirmField.value) {
        confirmField.classList.add('is-invalid');
        isValid = false;
    }

    return isValid;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Real-time form validation
function setupRealTimeValidation() {
    try {
        // Password strength checking
        const passwordField = document.getElementById('password');
        if (passwordField) {
            passwordField.addEventListener('input', function () {
                try {
                    updatePasswordStrength(this.value);
                } catch (error) {
                    console.error('Error in password strength update:', error);
                }
            });
        }

        // Password confirmation checking
        const confirmField = document.getElementById('password_confirm');
        const originalPasswordField = document.getElementById('password');

        if (confirmField && originalPasswordField) {
            confirmField.addEventListener('input', function () {
                if (this.value && originalPasswordField.value !== this.value) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        }

        // Email validation
        const emailFields = document.querySelectorAll('input[type="email"]');
        emailFields.forEach(field => {
            field.addEventListener('blur', function () {
                if (this.value && !isValidEmail(this.value)) {
                    this.classList.add('is-invalid');
                } else {
                    this.classList.remove('is-invalid');
                }
            });
        });

        // Remove invalid class on input
        const allInputs = document.querySelectorAll('.form-control, .form-select');
        allInputs.forEach(input => {
            input.addEventListener('input', function () {
                if (this.classList.contains('is-invalid') && this.value.trim()) {
                    this.classList.remove('is-invalid');
                }
            });
        });
    } catch (error) {
        console.error('Error setting up real-time validation:', error);
    }
}

// Flash message auto-hide
function setupFlashMessages() {
    const flashMessages = document.querySelectorAll('.alert');
    flashMessages.forEach(message => {
        // Auto-hide success and info messages after 5 seconds
        if (message.classList.contains('alert-success') || message.classList.contains('alert-info')) {
            setTimeout(() => {
                message.style.opacity = '0';
                setTimeout(() => {
                    message.remove();
                }, 300);
            }, 5000);
        }
    });
}

// Profile preferences management
class ProfileManager {
    constructor() {
        this.setupEventListeners();
    }

    setupEventListeners() {
        // Email preferences toggle
        const emailToggles = document.querySelectorAll('.email-preference-group .form-check-input');
        emailToggles.forEach(toggle => {
            toggle.addEventListener('change', this.handleEmailPreferenceChange.bind(this));
        });

        // Auto-save preferences
        const preferenceInputs = document.querySelectorAll('.profile-form input, .profile-form select');
        preferenceInputs.forEach(input => {
            input.addEventListener('change', this.debounce(this.autoSavePreferences.bind(this), 1000));
        });
    }

    handleEmailPreferenceChange(event) {
        const toggle = event.target;
        const isEnabled = toggle.checked;

        // Visual feedback
        const label = toggle.nextElementSibling;
        if (label) {
            label.style.opacity = isEnabled ? '1' : '0.6';
        }

        // Update frequency dropdown state
        const frequencySelect = document.getElementById('email_frequency');
        if (frequencySelect) {
            const anyEnabled = document.querySelectorAll('.email-preference-group .form-check-input:checked').length > 0;
            frequencySelect.disabled = !anyEnabled;
        }
    }

    async autoSavePreferences() {
        try {
            const formData = new FormData(document.querySelector('.profile-form'));
            const preferences = {};

            for (let [key, value] of formData.entries()) {
                if (key.includes('email') || key.includes('preference')) {
                    preferences[key] = value;
                }
            }

            const response = await fetch('/auth/api/email-preferences', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(preferences)
            });

            if (response.ok) {
                this.showSaveIndicator('Preferences saved automatically', 'success');
            }
        } catch (error) {
            console.error('Error auto-saving preferences:', error);
        }
    }

    showSaveIndicator(message, type = 'info') {
        // Create or update save indicator
        let indicator = document.getElementById('save-indicator');
        if (!indicator) {
            indicator = document.createElement('div');
            indicator.id = 'save-indicator';
            indicator.className = 'alert alert-' + type + ' alert-dismissible fade show position-fixed';
            indicator.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 250px;';
            document.body.appendChild(indicator);
        }

        indicator.innerHTML = `
            <i class="bi bi-check-circle me-2"></i>${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Auto-hide after 3 seconds
        setTimeout(() => {
            if (indicator && indicator.parentNode) {
                indicator.remove();
            }
        }, 3000);
    }

    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
}

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function () {
    setupRealTimeValidation();
    setupFlashMessages();

    // Initialize profile manager if on profile page
    if (document.querySelector('.profile-form')) {
        new ProfileManager();
    }

    // Form submission validation
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function (e) {
            if (!validateForm(this.id)) {
                e.preventDefault();

                // Scroll to first invalid field
                const firstInvalid = this.querySelector('.is-invalid');
                if (firstInvalid) {
                    firstInvalid.scrollIntoView({ behavior: 'smooth', block: 'center' });
                    firstInvalid.focus();
                }
            }
        });
    });

    // Loading states for buttons
    const submitButtons = document.querySelectorAll('button[type="submit"]');
    submitButtons.forEach(button => {
        button.addEventListener('click', function () {
            const form = this.closest('form');
            if (form && validateForm(form.id)) {
                this.disabled = true;
                this.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Processing...';

                // Re-enable after 10 seconds as fallback
                setTimeout(() => {
                    this.disabled = false;
                    this.innerHTML = this.getAttribute('data-original-text') || 'Submit';
                }, 10000);
            }
        });

        // Store original text
        button.setAttribute('data-original-text', button.innerHTML);
    });
});

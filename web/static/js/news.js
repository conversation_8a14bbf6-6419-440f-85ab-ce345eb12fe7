// News Display Functionality

// Utility function to convert a value to a color on a red-to-green gradient
function getColorFromValue(value, min, max) {
    // Clamp value between min and max
    const clampedValue = Math.max(min, Math.min(max, value));

    // Convert to a value between 0 and 1
    const normalizedValue = (clampedValue - min) / (max - min);

    // Calculate RGB values for a red-to-green gradient
    const r = Math.round(255 * (1 - normalizedValue));
    const g = Math.round(255 * normalizedValue);

    // Return as rgb color (b is always 0)
    return `rgb(${r},${g},0)`;
}

document.addEventListener('DOMContentLoaded', function () {
    // DOM elements
    const newsContainer = document.getElementById('news-container');
    const newsStartDateInput = document.getElementById('news-start-date');
    const newsEndDateInput = document.getElementById('news-end-date');
    const filterNewsBtn = document.getElementById('filter-news-btn');
    const clearNewsFilterBtn = document.getElementById('clear-news-filter-btn');
    const newsSearchInput = document.getElementById('news-search');
    const clearSearchBtn = document.getElementById('clear-search-btn');

    // New filter elements (may not exist in current template)
    const applyFiltersBtn = document.getElementById('apply-filters-btn');
    const clearFiltersBtn = document.getElementById('clear-filters-btn');

    // Store the previously loaded articles for comparison
    let previousArticles = {};

    // Store the last check timestamp
    let lastCheckTimestamp = Math.floor(Date.now() / 1000);

    // Store all loaded articles
    let allLoadedArticles = [];

    // Store current filters
    let currentFilters = {
        startDate: null,
        endDate: null,
        search: null
    };

    // Lazy loading configuration
    const lazyLoadConfig = {
        initialBatchSize: 10,  // Number of articles to load initially
        batchSize: 5,          // Number of articles to load on each "load more" click
        currentIndex: 0        // Current index in the articles array
    };

    // Set default date values (last 7 days)
    const today = new Date();
    const sevenDaysAgo = new Date();
    sevenDaysAgo.setDate(today.getDate() - 7);

    // Format dates for input fields
    const formatDateForInput = (date) => {
        return date.toISOString().split('T')[0];
    };

    // Initialize date inputs
    newsEndDateInput.value = formatDateForInput(today);
    newsStartDateInput.value = formatDateForInput(sevenDaysAgo);

    // Add event listeners for date filter buttons
    filterNewsBtn.addEventListener('click', function () {
        const startDate = newsStartDateInput.value;
        const endDate = newsEndDateInput.value;

        // Update current filters
        currentFilters.startDate = startDate;
        currentFilters.endDate = endDate;

        // Load news with date filter
        loadAllNews(false, startDate, endDate, currentFilters.search);
    });

    clearNewsFilterBtn.addEventListener('click', function () {
        // Set date inputs to default values (last 7 days)
        const today = new Date();
        const sevenDaysAgo = new Date();
        sevenDaysAgo.setDate(today.getDate() - 7);

        // Format dates for input fields
        newsEndDateInput.value = formatDateForInput(today);
        newsStartDateInput.value = formatDateForInput(sevenDaysAgo);

        // Update current filters
        currentFilters.startDate = newsStartDateInput.value;
        currentFilters.endDate = newsEndDateInput.value;

        // Load news with default date filter
        loadAllNews(false, newsStartDateInput.value, newsEndDateInput.value, currentFilters.search);
    });

    // Add event listeners for search
    newsSearchInput.addEventListener('input', debounce(function () {
        currentFilters.search = newsSearchInput.value.trim();
        loadAllNews(
            false,
            newsStartDateInput.value,
            newsEndDateInput.value,
            currentFilters.search
        );
    }, 500));

    clearSearchBtn.addEventListener('click', function () {
        newsSearchInput.value = '';
        currentFilters.search = null;
        loadAllNews(
            false,
            newsStartDateInput.value,
            newsEndDateInput.value,
            null
        );
    });

    // Format date for display
    function formatDateForDisplay(dateStr) {
        if (!dateStr) return '';
        const date = new Date(dateStr);
        return date.toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    }

    // Load all news on page load with default date filter
    // Use current filters if available, otherwise use default
    if (currentFilters.startDate || currentFilters.endDate) {
        loadAllNews(
            false,
            currentFilters.startDate,
            currentFilters.endDate,
            currentFilters.search
        );
    } else {
        loadAllNews(
            false,
            newsStartDateInput.value,
            newsEndDateInput.value,
            currentFilters.search
        );
    }

    // Function to check for new articles by monitoring the output directory
    function checkForNewArticles() {
        console.log('Checking for new articles since:', new Date(lastCheckTimestamp * 1000).toLocaleString());

        // Call the API to check for new articles
        fetch(`/api/check-new-articles?last_check=${lastCheckTimestamp}`)
            .then(response => response.json())
            .then(data => {
                console.log('New articles check result:', data);

                // Update the last check timestamp
                lastCheckTimestamp = data.current_timestamp;

                // If there are new articles, refresh the news section
                if (data.has_new_articles) {
                    console.log(`Found ${data.new_article_count} new articles, refreshing news section`);

                    // Show a notification
                    const notification = document.createElement('div');
                    notification.className = 'toast show position-fixed top-0 end-0 m-3';
                    notification.setAttribute('role', 'alert');
                    notification.setAttribute('aria-live', 'assertive');
                    notification.setAttribute('aria-atomic', 'true');
                    notification.innerHTML = `
                        <div class="toast-header bg-success text-white">
                            <strong class="me-auto">New Articles</strong>
                            <small>Just now</small>
                            <button type="button" class="btn-close btn-close-white" data-bs-dismiss="toast" aria-label="Close"></button>
                        </div>
                        <div class="toast-body">
                            ${data.new_article_count} new articles have been added. Refreshing news section...
                        </div>
                    `;
                    document.body.appendChild(notification);

                    // Remove the notification after 5 seconds
                    setTimeout(() => {
                        notification.remove();
                    }, 5000);

                    // Refresh the news section with all current filters
                    loadAllNews(
                        true,
                        currentFilters.startDate,
                        currentFilters.endDate,
                        currentFilters.search
                    );

                    // Also refresh the graph to show new dates
                    try {
                        // Try different ways to access the fetchAvailableDates function
                        if (typeof window.fetchAvailableDates === 'function') {
                            window.fetchAvailableDates(true);
                        } else if (typeof fetchAvailableDates === 'function') {
                            fetchAvailableDates(true);
                        } else {
                            console.log('fetchAvailableDates function not found, skipping graph refresh');
                        }
                    } catch (error) {
                        console.error('Error refreshing graph:', error);
                    }
                }
            })
            .catch(error => {
                console.error('Error checking for new articles:', error);
            });
    }

    // Set up periodic check for new articles (every 30 seconds)
    setInterval(checkForNewArticles, 30 * 1000); // 30 seconds in milliseconds

    // Load all news articles
    async function loadAllNews(isRefresh = false, startDate = null, endDate = null, search = null) {
        console.log('Loading all news articles', isRefresh ? '(refresh)' : '',
            startDate ? `from ${startDate}` : '',
            endDate ? `to ${endDate}` : '',
            search ? `with search "${search}"` : '');

        // If this is a refresh, keep the current content visible while loading
        if (!isRefresh) {
            // Show loading message for initial load
            let loadingMessage = 'Loading news...';
            if (startDate && endDate) {
                loadingMessage = `Loading news from ${formatDateForDisplay(startDate)} to ${formatDateForDisplay(endDate)}...`;
            } else if (startDate) {
                loadingMessage = `Loading news from ${formatDateForDisplay(startDate)}...`;
            } else if (endDate) {
                loadingMessage = `Loading news until ${formatDateForDisplay(endDate)}...`;
            }
            if (search) {
                loadingMessage += ` Searching for "${search}"...`;
            }

            newsContainer.innerHTML = `<div class="text-center"><div class="spinner-border text-primary" role="status"><span class="visually-hidden">Loading...</span></div><p class="mt-2">${loadingMessage}</p></div>`;
        }

        try {
            // Build URL with query parameters
            let url = '/api/news';
            const params = new URLSearchParams();

            // Add date filter parameters if provided
            if (startDate) {
                params.append('start_date', startDate);
            }
            if (endDate) {
                params.append('end_date', endDate);
            }

            // Add search parameter if provided
            if (search) {
                params.append('search_keyword', search);
            }

            // Add the default limit - use a larger limit when filtering
            // to ensure we get enough articles that match the filters
            const isFiltering = startDate || endDate || search;
            const limit = isFiltering ? '100' : '20';
            params.append('limit', limit);

            // Add cache parameter - don't use cache when filtering
            // to ensure we get the most up-to-date results
            if (isFiltering) {
                params.append('use_cache', 'false');
            }

            // Add the parameters to the URL
            if (params.toString()) {
                url += '?' + params.toString();
            }

            console.log('Fetching all news from URL:', url);

            // Fetch data from the API
            const response = await fetch(url);

            if (!response.ok) {
                throw new Error(`Server returned ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Received news data:', data);

            if (data.error) {
                throw new Error(data.error);
            }

            // Check if data has the expected structure
            if (!data.all) {
                console.warn('News data is missing the "all" property, adding empty array');
                data.all = [];
            }

            // If this is a refresh, we need to compare with previous data
            if (isRefresh) {
                renderAllNewsWithAnimation(data);
            } else {
                // For initial load, just render normally
                renderAllNews(data);

                // Store the current articles for future comparison
                previousArticles = JSON.parse(JSON.stringify(data));
            }

            // Log the number of articles loaded
            console.log(`Loaded ${data.all.length} articles in total`);
        } catch (error) {
            console.error('Error fetching news:', error);
            newsContainer.innerHTML = `<div class="alert alert-danger">Error loading news: ${error.message}</div>`;
        }
    }

    // Keep the loadNewsForDate function for backward compatibility
    // but make it use the all-news endpoint with date filtering
    window.loadNewsForDate = function (date) {
        console.log('loadNewsForDate called with date:', date);

        // If a date is provided, use date filtering
        if (date) {
            // Format date as YYYY-MM-DD if it's not already
            let formattedDate = date;
            if (typeof date === 'object' && date instanceof Date) {
                formattedDate = formatDateForInput(date);
            } else if (typeof date === 'string' && date.includes('T')) {
                formattedDate = date.split('T')[0];
            }

            // Update the date inputs
            newsStartDateInput.value = formattedDate;
            newsEndDateInput.value = formattedDate;

            // Update current filters
            currentFilters.startDate = formattedDate;
            currentFilters.endDate = formattedDate;

            // No longer updating selected date display - element has been removed

            // Use the all-news endpoint with date filtering
            // This ensures we use the same filtering logic for both methods
            loadAllNews(
                false,
                formattedDate,
                formattedDate,
                currentFilters.search
            );
        } else {
            // If no date is provided, clear the filter
            newsStartDateInput.value = '';
            newsEndDateInput.value = '';

            // Clear current date filters
            currentFilters.startDate = null;
            currentFilters.endDate = null;

            // Load all news without date filter but keep other filters
            loadAllNews(
                false,
                null,
                null,
                currentFilters.search
            );
        }
    };

    // Register the function with the global NewsMonitor object
    window.NewsMonitor.loadNewsForDate = window.loadNewsForDate;

    // Render all news articles with lazy loading
    function renderAllNews(data) {
        // Get the 'all' articles array which contains all articles
        const allArticles = data.all || [];

        // Store all articles for lazy loading
        allLoadedArticles = allArticles;

        // Reset lazy loading index
        lazyLoadConfig.currentIndex = 0;

        if (allArticles.length === 0) {
            // Check if this is a date range filter message
            if (data.system && data.system.length > 0 &&
                data.system[0].title && data.system[0].title.includes('No articles found for the selected date range')) {
                // Display a more user-friendly message for date range filtering
                const dateRangeStr = data.system[0].title.replace('No articles found for the selected date range: ', '');
                newsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h5>No articles found</h5>
                        <p>No news articles found for the date range: <strong>${dateRangeStr}</strong></p>
                        <p>Try selecting a different date range or source.</p>
                    </div>
                `;
            } else {
                // Regular "no articles" message
                newsContainer.innerHTML = '<div class="alert alert-info">No news articles found.</div>';
            }
            return;
        }

        // Create HTML for news articles
        let html = '';

        // Create a container for the articles
        html += '<div id="news-articles-container"></div>';

        // Add a "Load More" button if there are more articles than the initial batch
        if (allArticles.length > lazyLoadConfig.initialBatchSize) {
            html += `
                <div class="text-center mt-4 mb-3">
                    <button id="load-more-btn" class="btn btn-primary">
                        Load More Articles
                    </button>
                </div>
            `;
        }

        // Set the container HTML
        newsContainer.innerHTML = html;

        // Get the articles container
        const articlesContainer = document.getElementById('news-articles-container');

        // Load the initial batch of articles
        const initialBatch = allArticles.slice(0, lazyLoadConfig.initialBatchSize);
        let articlesHtml = '';

        initialBatch.forEach(article => {
            articlesHtml += createArticleHtml(article);
        });

        articlesContainer.innerHTML = articlesHtml;

        // Update the current index
        lazyLoadConfig.currentIndex = initialBatch.length;

        // Add event listener to the "Load More" button
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', loadMoreArticles);
        }
    }

    // Load more articles when the "Load More" button is clicked
    function loadMoreArticles() {
        // Get the articles container
        const articlesContainer = document.getElementById('news-articles-container');

        // Calculate the end index for the next batch
        const startIndex = lazyLoadConfig.currentIndex;
        const endIndex = Math.min(startIndex + lazyLoadConfig.batchSize, allLoadedArticles.length);

        // Get the next batch of articles
        const nextBatch = allLoadedArticles.slice(startIndex, endIndex);

        // Create HTML for the next batch
        let batchHtml = '';
        nextBatch.forEach(article => {
            batchHtml += createArticleHtml(article);
        });

        // Append the new articles to the container
        articlesContainer.innerHTML += batchHtml;

        // Update the current index
        lazyLoadConfig.currentIndex = endIndex;

        // Hide the "Load More" button if we've loaded all articles
        if (lazyLoadConfig.currentIndex >= allLoadedArticles.length) {
            const loadMoreBtn = document.getElementById('load-more-btn');
            if (loadMoreBtn) {
                loadMoreBtn.parentNode.innerHTML = '<p class="text-muted">No more articles to load</p>';
            }
        }
    }

    // Render all news with animation for new articles
    function renderAllNewsWithAnimation(data) {
        // Get the 'all' articles array which contains all articles
        const allArticles = data.all || [];

        // Store all articles for lazy loading
        allLoadedArticles = allArticles;

        // Reset lazy loading index
        lazyLoadConfig.currentIndex = 0;

        if (allArticles.length === 0) {
            // Check if this is a date range filter message
            if (data.system && data.system.length > 0 &&
                data.system[0].title && data.system[0].title.includes('No articles found for the selected date range')) {
                // Display a more user-friendly message for date range filtering
                const dateRangeStr = data.system[0].title.replace('No articles found for the selected date range: ', '');
                newsContainer.innerHTML = `
                    <div class="alert alert-warning">
                        <h5>No articles found</h5>
                        <p>No news articles found for the date range: <strong>${dateRangeStr}</strong></p>
                        <p>Try selecting a different date range or source.</p>
                    </div>
                `;
            } else {
                // Regular "no articles" message
                newsContainer.innerHTML = '<div class="alert alert-info">No news articles found.</div>';
            }
            return;
        }

        // Process each article to check if it's new
        allArticles.forEach(article => {
            // Simple check for new articles - if it wasn't in the previous data
            if (previousArticles.all) {
                article.isNew = !previousArticles.all.some(prevArticle =>
                    prevArticle.url === article.url
                );
            } else {
                article.isNew = true;
            }
        });

        // Create HTML for news articles
        let html = '';

        // Create a container for the articles
        html += '<div id="news-articles-container"></div>';

        // Add a "Load More" button if there are more articles than the initial batch
        if (allArticles.length > lazyLoadConfig.initialBatchSize) {
            html += `
                <div class="text-center mt-4 mb-3">
                    <button id="load-more-btn" class="btn btn-primary">
                        Load More Articles
                    </button>
                </div>
            `;
        }

        // Update the container with new content
        newsContainer.innerHTML = html;

        // Get the articles container
        const articlesContainer = document.getElementById('news-articles-container');

        // Load the initial batch of articles
        const initialBatch = allArticles.slice(0, lazyLoadConfig.initialBatchSize);
        let articlesHtml = '';

        initialBatch.forEach(article => {
            articlesHtml += createArticleHtml(article, article.isNew);
        });

        articlesContainer.innerHTML = articlesHtml;

        // Update the current index
        lazyLoadConfig.currentIndex = initialBatch.length;

        // Add event listener to the "Load More" button
        const loadMoreBtn = document.getElementById('load-more-btn');
        if (loadMoreBtn) {
            loadMoreBtn.addEventListener('click', loadMoreArticles);
        }

        // Add a highlight animation to the container
        newsContainer.classList.add('highlight-refresh');

        // Remove the highlight class after animation completes
        setTimeout(() => {
            newsContainer.classList.remove('highlight-refresh');
        }, 2000);

        // Store the current articles for future comparison
        previousArticles = JSON.parse(JSON.stringify(data));
    }

    // Format source name for display
    function formatSourceName(source) {
        if (!source) return 'Unknown Source';

        return source
            .split('_')
            .map(word => word.charAt(0).toUpperCase() + word.slice(1))
            .join(' ');
    }

    // Create HTML for a single article
    function createArticleHtml(article, isNew = false) {
        // Extract article data with fallbacks for missing fields
        let title = article.title || 'No Title';
        const url = article.url || '#';
        const author = article.author || 'Unknown Author';
        const source = article.source ? formatSourceName(article.source) : 'Unknown Source';
        const publishTime = article.publish_time ? formatDateTime(article.publish_time) : 'Unknown Date';
        const content = article.content || 'No content available.';
        let summary = article.summary || '';
        const categories = article.category ? [article.category] : [];
        const tags = article.tags || [];

        // Highlight matched keywords if present
        if (article.matched_keywords) {
            const keyword = article.matched_keywords.keyword;
            const regex = new RegExp(`(${keyword})`, 'gi');

            if (article.matched_keywords.title) {
                title = title.replace(regex, '<mark>$1</mark>');
            }
            if (article.matched_keywords.summary) {
                summary = summary.replace(regex, '<mark>$1</mark>');
            }
        }

        // Combine categories and tags
        const allTags = [...new Set([...categories, ...tags])].filter(tag => tag && tag.trim() !== '');

        // Create tags HTML
        let tagsHtml = '';
        if (allTags.length > 0) {
            tagsHtml = '<div class="mb-2">';
            allTags.forEach(tag => {
                tagsHtml += `<span class="news-category">${tag}</span>`;
            });
            tagsHtml += '</div>';
        }

        // Create summary HTML
        let summaryHtml = '';
        if (summary && summary.trim() !== '') {
            summaryHtml = `<p class="news-summary">${summary}</p>`;
        }

        // Add animation classes for new articles
        const animationClass = isNew ? 'animate__animated animate__fadeInDown news-item-new' : '';

        // Add a "New" badge for new articles
        const newBadge = isNew ? '<span class="badge bg-success ms-2 animate__animated animate__fadeIn">New!</span>' : '';

        // Create the article HTML
        return `
            <div class="news-item ${animationClass}">
                <h5 class="news-title">
                    <a href="${url}" target="_blank" class="news-title-link">${title}</a>
                    ${newBadge}
                </h5>
                <div class="d-flex justify-content-between mb-2">
                    <div>
                        <span class="news-author">${author}</span>
                        <span class="news-source badge bg-light text-dark ms-2">${source}</span>
                    </div>
                    <span class="news-date">${publishTime}</span>
                </div>
                ${tagsHtml}
                ${summaryHtml}
                <p class="news-content">${truncateText(content, 300)}</p>
            </div>
        `;
    }

    // Format date and time for display
    function formatDateTime(dateTimeStr) {
        try {
            const date = new Date(dateTimeStr);
            return date.toLocaleString('en-US', {
                year: 'numeric',
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (e) {
            return dateTimeStr;
        }
    }

    // Truncate text to a specific length
    function truncateText(text, maxLength) {
        if (!text) return '';

        if (text.length <= maxLength) {
            return text;
        }

        return text.substring(0, maxLength) + '...';
    }

    // Utility function for debouncing
    function debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }
});

"""
Email service for the NewsMonitor web application.

This module provides email functionality including daily summaries,
market alerts, and user notifications.
"""

import os
import smtplib
from datetime import datetime, timedelta
from email.mime.text import MIMEText
from email.mime.multipart import MI<PERSON><PERSON><PERSON>ipart
from email.mime.base import MIMEBase
from email import encoders
from typing import List, Dict, Any, Optional
from flask import current_app, render_template
from flask_mail import Mail, Message

from db.models import User, EmailLog
from db.database import DatabaseManager
from web.data.news_data import get_all_news
from web.data.sp500_data import get_sp500_data
from web.data.prediction_service import get_prediction, get_llm_prediction
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)


class EmailService:
    """Email service for sending various types of emails."""

    def __init__(self, app=None):
        self.app = app
        self.mail = None
        if app:
            self.init_app(app)

    def init_app(self, app):
        """Initialize the email service with Flask app."""
        self.app = app

        # Configure Flask-Mail
        app.config.setdefault('MAIL_SERVER', os.environ.get(
            'MAIL_SERVER', 'smtp.gmail.com'))
        app.config.setdefault('MAIL_PORT', int(
            os.environ.get('MAIL_PORT', 587)))
        app.config.setdefault('MAIL_USE_TLS', os.environ.get(
            'MAIL_USE_TLS', 'true').lower() == 'true')
        app.config.setdefault('MAIL_USE_SSL', os.environ.get(
            'MAIL_USE_SSL', 'false').lower() == 'true')
        app.config.setdefault('MAIL_USERNAME', os.environ.get('MAIL_USERNAME'))
        app.config.setdefault('MAIL_PASSWORD', os.environ.get('MAIL_PASSWORD'))
        app.config.setdefault('MAIL_DEFAULT_SENDER', os.environ.get(
            'MAIL_DEFAULT_SENDER', '<EMAIL>'))

        self.mail = Mail(app)

    def send_email(self, recipient: str, subject: str, html_body: str, text_body: str = None, user_id: int = None, email_type: str = 'general') -> bool:
        """Send an email and log the result."""
        try:
            msg = Message(
                subject=subject,
                recipients=[recipient],
                html=html_body,
                body=text_body or self._html_to_text(html_body)
            )

            self.mail.send(msg)

            # Log successful email
            self._log_email(user_id, email_type, subject, recipient, 'sent')
            logger.info(f"Email sent successfully to {recipient}: {subject}")
            return True

        except Exception as e:
            # Log failed email
            self._log_email(user_id, email_type, subject,
                            recipient, 'failed', str(e))
            logger.error(f"Failed to send email to {recipient}: {e}")
            return False

    def send_daily_summary(self, user_or_id) -> bool:
        """Send daily market summary to a user."""
        try:
            # Handle both User objects and user IDs
            if isinstance(user_or_id, User):
                user_id = user_or_id.id
                user_email = user_or_id.email
                user_preferences = user_or_id.email_preferences
                user_obj = user_or_id
            else:
                # Query user from database in our own session
                user_id = user_or_id
                db = DatabaseManager()
                with db.connection.get_session() as session:
                    user_obj = session.query(User).filter(
                        User.id == user_id).first()
                    if not user_obj:
                        logger.error(f"User {user_id} not found")
                        return False
                    user_email = user_obj.email
                    user_preferences = user_obj.email_preferences
                    # Create a detached copy of user data
                    user_data = {
                        'id': user_obj.id,
                        'username': user_obj.username,
                        'email': user_obj.email,
                        'first_name': user_obj.first_name,
                        'last_name': user_obj.last_name
                    }
                db.close()

                # Create a simple object for template rendering
                class UserData:
                    def __init__(self, data):
                        for key, value in data.items():
                            setattr(self, key, value)
                user_obj = UserData(user_data)

            # Check if user wants daily summaries
            if not user_preferences.get('daily_summary', True):
                return True

            # Check if we already sent a summary today
            if self._already_sent_today(user_id, 'daily_summary'):
                logger.info(
                    f"Daily summary already sent to user {user_id} today")
                return True

            # Generate summary data
            summary_data = self._generate_daily_summary_data()

            # Render email templates
            html_body = render_template('email/daily_summary.html',
                                        user=user_obj,
                                        data=summary_data)
            text_body = render_template('email/daily_summary.txt',
                                        user=user_obj,
                                        data=summary_data)

            subject = f"Daily Market Summary - {datetime.now().strftime('%B %d, %Y')}"

            return self.send_email(
                recipient=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user_id,
                email_type='daily_summary'
            )

        except Exception as e:
            logger.error(
                f"Error sending daily summary to user {user_id if 'user_id' in locals() else 'unknown'}: {e}")
            return False

    def send_market_alert(self, user_or_id, alert_data: Dict[str, Any]) -> bool:
        """Send market alert to a user."""
        try:
            # Handle both User objects and user IDs
            if isinstance(user_or_id, User):
                user_id = user_or_id.id
                user_email = user_or_id.email
                user_preferences = user_or_id.email_preferences
                user_obj = user_or_id
            else:
                # Query user from database in our own session
                user_id = user_or_id
                db = DatabaseManager()
                with db.connection.get_session() as session:
                    user_obj = session.query(User).filter(
                        User.id == user_id).first()
                    if not user_obj:
                        logger.error(f"User {user_id} not found")
                        return False
                    user_email = user_obj.email
                    user_preferences = user_obj.email_preferences
                    # Create a detached copy of user data
                    user_data = {
                        'id': user_obj.id,
                        'username': user_obj.username,
                        'email': user_obj.email,
                        'first_name': user_obj.first_name,
                        'last_name': user_obj.last_name
                    }
                db.close()

                # Create a simple object for template rendering
                class UserData:
                    def __init__(self, data):
                        for key, value in data.items():
                            setattr(self, key, value)
                user_obj = UserData(user_data)

            # Check if user wants market alerts
            if not user_preferences.get('market_alerts', True):
                return True

            # Render email templates
            html_body = render_template('email/market_alert.html',
                                        user=user_obj,
                                        alert=alert_data)
            text_body = render_template('email/market_alert.txt',
                                        user=user_obj,
                                        alert=alert_data)

            subject = f"Market Alert: {alert_data.get('title', 'Significant Market Movement')}"

            return self.send_email(
                recipient=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user_id,
                email_type='market_alert'
            )

        except Exception as e:
            logger.error(
                f"Error sending market alert to user {user_id if 'user_id' in locals() else 'unknown'}: {e}")
            return False

    def send_news_digest(self, user_or_id) -> bool:
        """Send news digest to a user."""
        try:
            # Handle both User objects and user IDs
            if isinstance(user_or_id, User):
                user_id = user_or_id.id
                user_email = user_or_id.email
                user_preferences = user_or_id.email_preferences
                user_obj = user_or_id
            else:
                # Query user from database in our own session
                user_id = user_or_id
                db = DatabaseManager()
                with db.connection.get_session() as session:
                    user_obj = session.query(User).filter(
                        User.id == user_id).first()
                    if not user_obj:
                        logger.error(f"User {user_id} not found")
                        return False
                    user_email = user_obj.email
                    user_preferences = user_obj.email_preferences
                    # Create a detached copy of user data
                    user_data = {
                        'id': user_obj.id,
                        'username': user_obj.username,
                        'email': user_obj.email,
                        'first_name': user_obj.first_name,
                        'last_name': user_obj.last_name
                    }
                db.close()

                # Create a simple object for template rendering
                class UserData:
                    def __init__(self, data):
                        for key, value in data.items():
                            setattr(self, key, value)
                user_obj = UserData(user_data)

            # Check if user wants news digest
            if not user_preferences.get('news_digest', True):
                return True

            # Check frequency
            frequency = user_preferences.get('frequency', 'daily')
            if not self._should_send_digest(user_id, frequency):
                return True

            # Get news data
            news_data = get_all_news(limit=10, start_date=(
                datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'))

            # Render email templates
            html_body = render_template('email/news_digest.html',
                                        user=user_obj,
                                        news=news_data)
            text_body = render_template('email/news_digest.txt',
                                        user=user_obj,
                                        news=news_data)

            subject = f"News Digest - {datetime.now().strftime('%B %d, %Y')}"

            return self.send_email(
                recipient=user_email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user_id,
                email_type='news_digest'
            )

        except Exception as e:
            logger.error(
                f"Error sending news digest to user {user_id if 'user_id' in locals() else 'unknown'}: {e}")
            return False

    def send_verification_email(self, user: User, token: str) -> bool:
        """Send email verification email."""
        try:
            verification_url = f"{current_app.config.get('BASE_URL', 'http://localhost:5000')}/auth/verify-email/{token}"

            html_body = render_template('email/verify_email.html',
                                        user=user,
                                        verification_url=verification_url)
            text_body = render_template('email/verify_email.txt',
                                        user=user,
                                        verification_url=verification_url)

            subject = "Verify Your Email Address - Market Monitor"

            return self.send_email(
                recipient=user.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user.id,
                email_type='verification'
            )

        except Exception as e:
            logger.error(
                f"Error sending verification email to user {user.id}: {e}")
            return False

    def send_password_reset_email(self, user: User, token: str) -> bool:
        """Send password reset email."""
        try:
            reset_url = f"{current_app.config.get('BASE_URL', 'http://localhost:5000')}/auth/reset-password/{token}"

            html_body = render_template('email/password_reset.html',
                                        user=user,
                                        reset_url=reset_url)
            text_body = render_template('email/password_reset.txt',
                                        user=user,
                                        reset_url=reset_url)

            subject = "Password Reset Request - Market Monitor"

            return self.send_email(
                recipient=user.email,
                subject=subject,
                html_body=html_body,
                text_body=text_body,
                user_id=user.id,
                email_type='password_reset'
            )

        except Exception as e:
            logger.error(
                f"Error sending password reset email to user {user.id}: {e}")
            return False

    def _generate_daily_summary_data(self) -> Dict[str, Any]:
        """Generate data for daily summary email."""
        try:
            # Get market data
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=7)
                          ).strftime('%Y-%m-%d')

            market_data = get_sp500_data(start_date, end_date)

            # Get recent news
            news_data = get_all_news(limit=5, start_date=(
                datetime.now() - timedelta(days=1)).strftime('%Y-%m-%d'))

            # Get predictions
            try:
                prediction_data = get_prediction()
            except Exception as e:
                logger.warning(f"Could not get prediction data: {e}")
                prediction_data = None

            # Get LLM prediction
            try:
                import asyncio
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                llm_prediction = loop.run_until_complete(get_llm_prediction())
            except Exception as e:
                logger.warning(f"Could not get LLM prediction: {e}")
                llm_prediction = None

            return {
                'date': datetime.now(),
                'market_data': market_data,
                'news': news_data,
                'prediction': prediction_data,
                'llm_prediction': llm_prediction
            }

        except Exception as e:
            logger.error(f"Error generating daily summary data: {e}")
            return {
                'date': datetime.now(),
                'error': str(e)
            }

    def _log_email(self, user_id: int, email_type: str, subject: str, recipient: str, status: str, error_message: str = None):
        """Log email sending attempt."""
        try:
            db = DatabaseManager()
            with db.connection.get_session() as session:
                email_log = EmailLog(
                    user_id=user_id,
                    email_type=email_type,
                    subject=subject,
                    recipient_email=recipient,
                    status=status,
                    sent_at=datetime.now(
                        datetime.timezone.utc) if status == 'sent' else None,
                    error_message=error_message,
                    created_at=datetime.now(datetime.timezone.utc)
                )
                session.add(email_log)
                session.commit()
        except Exception as e:
            logger.error(f"Error logging email: {e}")

    def _already_sent_today(self, user_id: int, email_type: str) -> bool:
        """Check if we already sent this type of email today."""
        try:
            db = DatabaseManager()
            with db.connection.get_session() as session:
                today = datetime.now().date()
                log = session.query(EmailLog).filter(
                    EmailLog.user_id == user_id,
                    EmailLog.email_type == email_type,
                    EmailLog.status == 'sent',
                    EmailLog.sent_at >= today
                ).first()
                return log is not None
        except Exception as e:
            logger.error(f"Error checking if email already sent: {e}")
            return False

    def _should_send_digest(self, user_id: int, frequency: str) -> bool:
        """Check if we should send digest based on frequency."""
        if frequency == 'never':
            return False

        if frequency == 'daily':
            return not self._already_sent_today(user_id, 'news_digest')

        if frequency == 'weekly':
            # Check if we sent in the last 7 days
            try:
                db = DatabaseManager()
                with db.connection.get_session() as session:
                    week_ago = datetime.now() - timedelta(days=7)
                    log = session.query(EmailLog).filter(
                        EmailLog.user_id == user_id,
                        EmailLog.email_type == 'news_digest',
                        EmailLog.status == 'sent',
                        EmailLog.sent_at >= week_ago
                    ).first()
                    return log is None
            except Exception as e:
                logger.error(f"Error checking weekly digest: {e}")
                return False

        return True

    def _html_to_text(self, html: str) -> str:
        """Convert HTML to plain text."""
        # Simple HTML to text conversion
        import re
        text = re.sub('<[^<]+?>', '', html)
        text = re.sub(r'\s+', ' ', text)
        return text.strip()


# Global email service instance
email_service = EmailService()

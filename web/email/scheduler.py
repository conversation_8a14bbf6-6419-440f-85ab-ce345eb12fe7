"""
Email scheduler for the NewsMonitor web application.

This module provides background task scheduling for sending daily summaries,
market alerts, and other automated emails.
"""

import os
import time
from datetime import datetime, timed<PERSON>ta
from typing import List
from celery import Celery
from flask import current_app

from db.models import User
from db.database import DatabaseManager
from web.email.service import EmailService
from utils.logging_config import get_web_logger

logger = get_web_logger(__name__)

# Initialize Celery


def make_celery(app):
    """Create Celery instance for Flask app."""
    celery = Celery(
        app.import_name,
        backend=app.config.get('CELERY_RESULT_BACKEND',
                               'redis://localhost:6379/0'),
        broker=app.config.get('CELERY_BROKER_URL', 'redis://localhost:6379/0')
    )

    class ContextTask(celery.Task):
        """Make celery tasks work with Flask app context."""

        def __call__(self, *args, **kwargs):
            with app.app_context():
                return self.run(*args, **kwargs)

    celery.Task = ContextTask
    return celery


def register_tasks():
    global celery

    @celery.task(bind=True, max_retries=3)
    def send_daily_summaries_task(self):
        """Celery task to send daily summaries to all subscribed users."""
        try:
            logger.info("Starting daily summaries task")

            # Get all active users who want daily summaries
            users = get_users_for_daily_summary()

            if not users:
                logger.info("No users found for daily summaries")
                return {"status": "success", "users_processed": 0}

            email_service = EmailService(current_app)
            success_count = 0
            error_count = 0

            for user in users:
                try:
                    if email_service.send_daily_summary(user):
                        success_count += 1
                        logger.info(f"Daily summary sent to user {user.id}")
                    else:
                        error_count += 1
                        logger.error(
                            f"Failed to send daily summary to user {user.id}")

                    # Small delay to avoid overwhelming the email server
                    time.sleep(1)

                except Exception as e:
                    error_count += 1
                    logger.error(
                        f"Error sending daily summary to user {user.id}: {e}")

            logger.info(
                f"Daily summaries task completed: {success_count} sent, {error_count} failed")

            return {
                "status": "success",
                "users_processed": len(users),
                "emails_sent": success_count,
                "errors": error_count
            }

        except Exception as e:
            logger.error(f"Error in daily summaries task: {e}")
            # Retry the task
            raise self.retry(countdown=60 * 5, exc=e)  # Retry after 5 minutes

    @celery.task(bind=True, max_retries=3)
    def send_weekly_digests_task(self):
        """Celery task to send weekly news digests to subscribed users."""
        try:
            logger.info("Starting weekly digests task")

            # Get all active users who want weekly digests
            users = get_users_for_weekly_digest()

            if not users:
                logger.info("No users found for weekly digests")
                return {"status": "success", "users_processed": 0}

            email_service = EmailService(current_app)
            success_count = 0
            error_count = 0

            for user in users:
                try:
                    if email_service.send_news_digest(user):
                        success_count += 1
                        logger.info(f"Weekly digest sent to user {user.id}")
                    else:
                        error_count += 1
                        logger.error(
                            f"Failed to send weekly digest to user {user.id}")

                    # Small delay to avoid overwhelming the email server
                    time.sleep(1)

                except Exception as e:
                    error_count += 1
                    logger.error(
                        f"Error sending weekly digest to user {user.id}: {e}")

            logger.info(
                f"Weekly digests task completed: {success_count} sent, {error_count} failed")

            return {
                "status": "success",
                "users_processed": len(users),
                "emails_sent": success_count,
                "errors": error_count
            }

        except Exception as e:
            logger.error(f"Error in weekly digests task: {e}")
            # Retry the task
            # Retry after 10 minutes
            raise self.retry(countdown=60 * 10, exc=e)

    @celery.task
    def send_market_alert_task(alert_data: dict):
        """Celery task to send market alerts to subscribed users."""
        try:
            logger.info(
                f"Starting market alert task: {alert_data.get('title', 'Unknown')}")

            # Get all active users who want market alerts
            users = get_users_for_market_alerts()

            if not users:
                logger.info("No users found for market alerts")
                return {"status": "success", "users_processed": 0}

            email_service = EmailService(current_app)
            success_count = 0
            error_count = 0

            for user in users:
                try:
                    if email_service.send_market_alert(user, alert_data):
                        success_count += 1
                        logger.info(f"Market alert sent to user {user.id}")
                    else:
                        error_count += 1
                        logger.error(
                            f"Failed to send market alert to user {user.id}")

                    # Small delay to avoid overwhelming the email server
                    time.sleep(0.5)

                except Exception as e:
                    error_count += 1
                    logger.error(
                        f"Error sending market alert to user {user.id}: {e}")

            logger.info(
                f"Market alert task completed: {success_count} sent, {error_count} failed")

            return {
                "status": "success",
                "users_processed": len(users),
                "emails_sent": success_count,
                "errors": error_count
            }

        except Exception as e:
            logger.error(f"Error in market alert task: {e}")
            return {"status": "error", "message": str(e)}

    @celery.task
    def cleanup_old_email_logs_task():
        """Celery task to clean up old email logs."""
        try:
            logger.info("Starting email logs cleanup task")

            # Delete email logs older than 90 days
            cutoff_date = datetime.utcnow() - timedelta(days=90)

            db = DatabaseManager()
            with db.connection.get_session() as session:
                from db.models import EmailLog
                deleted_count = session.query(EmailLog).filter(
                    EmailLog.created_at < cutoff_date
                ).delete()
                session.commit()

            logger.info(
                f"Email logs cleanup completed: {deleted_count} logs deleted")

            return {
                "status": "success",
                "logs_deleted": deleted_count
            }

        except Exception as e:
            logger.error(f"Error in email logs cleanup task: {e}")
            return {"status": "error", "message": str(e)}


# Global celery instance (will be initialized with app)
celery = None


def init_scheduler(app):
    """Initialize the email scheduler with Flask app."""
    global celery

    # Configure Celery
    app.config.setdefault('CELERY_BROKER_URL', os.environ.get(
        'CELERY_BROKER_URL', 'redis://localhost:6379/0'))
    app.config.setdefault('CELERY_RESULT_BACKEND', os.environ.get(
        'CELERY_RESULT_BACKEND', 'redis://localhost:6379/0'))
    app.config.setdefault('CELERY_TIMEZONE', 'UTC')
    app.config.setdefault('CELERY_ENABLE_UTC', True)

    celery = make_celery(app)

    register_tasks()

    # Configure periodic tasks
    celery.conf.beat_schedule = {
        'send-daily-summaries': {
            'task': 'web.email.scheduler.send_daily_summaries_task',
            'schedule': 60.0 * 60.0 * 24.0,  # Every 24 hours
            'options': {'expires': 60.0 * 60.0 * 2.0}  # Expire after 2 hours
        },
        'send-weekly-digests': {
            'task': 'web.email.scheduler.send_weekly_digests_task',
            'schedule': 60.0 * 60.0 * 24.0 * 7.0,  # Every 7 days
            'options': {'expires': 60.0 * 60.0 * 4.0}  # Expire after 4 hours
        },
        'cleanup-old-email-logs': {
            'task': 'web.email.scheduler.cleanup_old_email_logs_task',
            'schedule': 60.0 * 60.0 * 24.0,  # Every 24 hours
            'options': {'expires': 60.0 * 60.0 * 1.0}  # Expire after 1 hour
        }
    }

    return celery


def get_users_for_daily_summary() -> List[User]:
    """Get all users who should receive daily summaries."""
    try:
        db = DatabaseManager()
        with db.connection.get_session() as session:
            users = session.query(User).filter(
                User.is_active == True,
                User.is_verified == True,
                User.email_preferences['daily_summary'].astext.cast(
                    db.connection.engine.dialect.BOOLEAN) == True,
                User.email_preferences['frequency'].astext == 'daily'
            ).all()
            return users
    except Exception as e:
        logger.error(f"Error getting users for daily summary: {e}")
        return []


def get_users_for_weekly_digest() -> List[User]:
    """Get all users who should receive weekly digests."""
    try:
        db = DatabaseManager()
        with db.connection.get_session() as session:
            users = session.query(User).filter(
                User.is_active == True,
                User.is_verified == True,
                User.email_preferences['news_digest'].astext.cast(
                    db.connection.engine.dialect.BOOLEAN) == True,
                User.email_preferences['frequency'].astext == 'weekly'
            ).all()
            return users
    except Exception as e:
        logger.error(f"Error getting users for weekly digest: {e}")
        return []


def get_users_for_market_alerts() -> List[User]:
    """Get all users who should receive market alerts."""
    try:
        db = DatabaseManager()
        with db.connection.get_session() as session:
            users = session.query(User).filter(
                User.is_active == True,
                User.is_verified == True,
                User.email_preferences['market_alerts'].astext.cast(
                    db.connection.engine.dialect.BOOLEAN) == True
            ).all()
            return users
    except Exception as e:
        logger.error(f"Error getting users for market alerts: {e}")
        return []


def trigger_market_alert(title: str, message: str, severity: str = 'medium'):
    """Trigger a market alert to be sent to all subscribed users."""
    alert_data = {
        'title': title,
        'message': message,
        'severity': severity,
        'timestamp': datetime.utcnow().isoformat()
    }

    # Send the alert asynchronously
    send_market_alert_task.delay(alert_data)
    logger.info(f"Market alert triggered: {title}")

# Simple scheduler for environments without Celery


class SimpleScheduler:
    """Simple scheduler for environments without Redis/Celery."""

    def __init__(self, app):
        self.app = app
        self.last_daily_run = None
        self.last_weekly_run = None

    def check_and_run_tasks(self):
        """Check if any scheduled tasks need to run."""
        now = datetime.utcnow()

        # Check daily summaries (run once per day at 8 AM UTC)
        if (self.last_daily_run is None or
                (now - self.last_daily_run).days >= 1 and now.hour >= 8):
            self.run_daily_summaries()
            self.last_daily_run = now

        # Check weekly digests (run once per week on Monday at 9 AM UTC)
        if (self.last_weekly_run is None or
            (now - self.last_weekly_run).days >= 7 and
                now.weekday() == 0 and now.hour >= 9):
            self.run_weekly_digests()
            self.last_weekly_run = now

    def run_daily_summaries(self):
        """Run daily summaries without Celery."""
        with self.app.app_context():
            try:
                users = get_users_for_daily_summary()
                email_service = EmailService(self.app)

                for user in users:
                    try:
                        email_service.send_daily_summary(user)
                        time.sleep(1)  # Rate limiting
                    except Exception as e:
                        logger.error(
                            f"Error sending daily summary to user {user.id}: {e}")

            except Exception as e:
                logger.error(f"Error in simple daily summaries: {e}")

    def run_weekly_digests(self):
        """Run weekly digests without Celery."""
        with self.app.app_context():
            try:
                users = get_users_for_weekly_digest()
                email_service = EmailService(self.app)

                for user in users:
                    try:
                        email_service.send_news_digest(user)
                        time.sleep(1)  # Rate limiting
                    except Exception as e:
                        logger.error(
                            f"Error sending weekly digest to user {user.id}: {e}")

            except Exception as e:
                logger.error(f"Error in simple weekly digests: {e}")

MARKET MONITOR - DAILY SUMMARY
{{ data.date.strftime('%B %d, %Y') }}

Hello {{ user.first_name or user.username }}!

{% if data.error %}
NOTICE: We encountered an issue generating your daily summary. Please visit our website for the latest market information.
{% else %}

MARKET OVERVIEW
===============
{% if data.market_data %}
Current Price: ${{ "%.2f"|format(data.market_data.current_price or 0) }}
{% if data.market_data.price_change %}
Change: {{ "%.2f"|format(data.market_data.price_change) }} ({{ "%.2f"|format(data.market_data.price_change_percent) }}%)
{% endif %}
Volume: {{ "{:,}"|format(data.market_data.volume or 0) }}
High: ${{ "%.2f"|format(data.market_data.high or 0) }}
Low: ${{ "%.2f"|format(data.market_data.low or 0) }}
{% else %}
Market data unavailable
{% endif %}

MARKET PREDICTIONS
==================
{% if data.prediction %}
Traditional Model: {{ data.prediction.prediction_label or 'N/A' }}
Confidence: {{ "%.1f"|format((data.prediction.confidence or 0) * 100) }}%
{% endif %}

{% if data.llm_prediction %}
AI Analysis: {{ data.llm_prediction.prediction or 'N/A' }}
{% if data.llm_prediction.confidence_scores %}
Confidence Scores:
{% for label, score in data.llm_prediction.confidence_scores.items() %}
  {{ label }}: {{ "%.1f"|format(score * 100) }}%
{% endfor %}
{% endif %}
{% endif %}

TOP FINANCIAL NEWS
==================
{% if data.news and data.news.all %}
{% for article in data.news.all[:5] %}
{{ loop.index }}. {{ article.title }}
   Source: {{ article.source }} | Date: {{ article.date.strftime('%B %d, %Y') if article.date else 'Recent' }}
   {% if article.content %}
   Summary: {{ article.content[:100] }}{% if article.content|length > 100 %}...{% endif %}
   {% endif %}
   URL: {{ article.url }}

{% endfor %}
{% else %}
No recent news available
{% endif %}

{% endif %}

VIEW FULL DASHBOARD
===================
Visit our website for detailed charts, analysis, and more:
{{ url_for('index', _external=True) }}

MANAGE PREFERENCES
==================
Update your email preferences or unsubscribe:
{{ url_for('auth.profile', _external=True) }}

---
This email was sent to {{ user.email }} because you subscribed to daily market summaries.
© {{ data.date.year }} Market Monitor. All rights reserved.

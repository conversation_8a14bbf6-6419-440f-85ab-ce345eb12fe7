{% extends "layout.html" %}

{% block title %}Profile - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-3">
        <!-- Profile Sidebar -->
        <div class="profile-sidebar">
            <div class="profile-avatar">
                <i class="bi bi-person-circle"></i>
                <h5>{{ current_user.first_name or current_user.username }}</h5>
                <p class="text-muted">{{ current_user.email }}</p>
            </div>
            
            <nav class="profile-nav">
                <a href="#profile-info" class="profile-nav-link active" data-tab="profile-info">
                    <i class="bi bi-person"></i> Profile Information
                </a>
                <a href="#email-preferences" class="profile-nav-link" data-tab="email-preferences">
                    <i class="bi bi-envelope"></i> Email Preferences
                </a>
                <a href="#security" class="profile-nav-link" data-tab="security">
                    <i class="bi bi-shield-lock"></i> Security
                </a>
            </nav>
        </div>
    </div>
    
    <div class="col-md-9">
        <!-- Profile Information Tab -->
        <div class="profile-tab active" id="profile-info">
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4><i class="bi bi-person"></i> Profile Information</h4>
                    <p class="text-muted">Update your personal information and preferences</p>
                </div>
                
                <form method="POST" class="profile-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.first_name.label(class="form-label") }}
                                {{ form.first_name(class="form-control") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.last_name.label(class="form-label") }}
                                {{ form.last_name(class="form-control") }}
                            </div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.email.label(class="form-label") }}
                        {{ form.email(class="form-control") }}
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.theme.label(class="form-label") }}
                                {{ form.theme(class="form-select") }}
                            </div>
                        </div>
                        
                        <div class="col-md-6">
                            <div class="form-group">
                                {{ form.timezone.label(class="form-label") }}
                                {{ form.timezone(class="form-select") }}
                            </div>
                        </div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Profile
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Email Preferences Tab -->
        <div class="profile-tab" id="email-preferences">
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4><i class="bi bi-envelope"></i> Email Preferences</h4>
                    <p class="text-muted">Manage your email notifications and subscriptions</p>
                </div>
                
                <form method="POST" class="profile-form">
                    {{ form.hidden_tag() }}
                    
                    <div class="email-preference-group">
                        <h6>Email Notifications</h6>
                        
                        <div class="form-check form-switch">
                            {{ form.daily_summary(class="form-check-input") }}
                            {{ form.daily_summary.label(class="form-check-label") }}
                            <div class="form-text">Receive daily market summaries and insights</div>
                        </div>
                        
                        <div class="form-check form-switch">
                            {{ form.market_alerts(class="form-check-input") }}
                            {{ form.market_alerts.label(class="form-check-label") }}
                            <div class="form-text">Get alerts for significant market movements</div>
                        </div>
                        
                        <div class="form-check form-switch">
                            {{ form.news_digest(class="form-check-input") }}
                            {{ form.news_digest.label(class="form-check-label") }}
                            <div class="form-text">Receive curated news digests</div>
                        </div>
                    </div>
                    
                    <div class="form-group">
                        {{ form.email_frequency.label(class="form-label") }}
                        {{ form.email_frequency(class="form-select") }}
                        <div class="form-text">How often you want to receive emails</div>
                    </div>
                    
                    <button type="submit" class="btn btn-primary">
                        <i class="bi bi-check-circle"></i> Update Preferences
                    </button>
                </form>
            </div>
        </div>
        
        <!-- Security Tab -->
        <div class="profile-tab" id="security">
            <div class="profile-card">
                <div class="profile-card-header">
                    <h4><i class="bi bi-shield-lock"></i> Security Settings</h4>
                    <p class="text-muted">Manage your account security and password</p>
                </div>
                
                <div class="security-info">
                    <div class="security-item">
                        <div class="security-item-icon">
                            <i class="bi bi-key"></i>
                        </div>
                        <div class="security-item-content">
                            <h6>Password</h6>
                            <p class="text-muted">Last changed: {{ current_user.updated_at.strftime('%B %d, %Y') if current_user.updated_at else 'Never' }}</p>
                        </div>
                        <div class="security-item-action">
                            <a href="{{ url_for('auth.change_password') }}" class="btn btn-outline-primary btn-sm">
                                Change Password
                            </a>
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-item-icon">
                            <i class="bi bi-envelope-check"></i>
                        </div>
                        <div class="security-item-content">
                            <h6>Email Verification</h6>
                            <p class="text-muted">
                                {% if current_user.is_verified %}
                                    <span class="text-success">Verified</span>
                                {% else %}
                                    <span class="text-warning">Not verified</span>
                                {% endif %}
                            </p>
                        </div>
                        <div class="security-item-action">
                            {% if not current_user.is_verified %}
                                <button class="btn btn-outline-warning btn-sm" onclick="resendVerification()">
                                    Resend Verification
                                </button>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="security-item">
                        <div class="security-item-icon">
                            <i class="bi bi-clock-history"></i>
                        </div>
                        <div class="security-item-content">
                            <h6>Last Login</h6>
                            <p class="text-muted">{{ current_user.last_login.strftime('%B %d, %Y at %I:%M %p') if current_user.last_login else 'Never' }}</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
<script>
// Profile tab switching
document.addEventListener('DOMContentLoaded', function() {
    const navLinks = document.querySelectorAll('.profile-nav-link');
    const tabs = document.querySelectorAll('.profile-tab');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            // Remove active class from all links and tabs
            navLinks.forEach(l => l.classList.remove('active'));
            tabs.forEach(t => t.classList.remove('active'));
            
            // Add active class to clicked link
            this.classList.add('active');
            
            // Show corresponding tab
            const tabId = this.getAttribute('data-tab');
            document.getElementById(tabId).classList.add('active');
        });
    });
});

function resendVerification() {
    // TODO: Implement resend verification email
    alert('Verification email sent!');
}
</script>
{% endblock %}

{% extends "layout.html" %}

{% block title %}Login - Market Monitor{% endblock %}

{% block head %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/auth.css') }}">
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-6 col-lg-4">
        <div class="auth-card">
            <div class="auth-header">
                <h2><i class="bi bi-person-circle"></i> Login</h2>
                <p class="text-muted">Welcome back to Market Monitor</p>
            </div>
            
            <form method="POST" class="auth-form">
                {{ form.hidden_tag() }}
                
                <div class="form-group">
                    {{ form.username.label(class="form-label") }}
                    {{ form.username(class="form-control" + (" is-invalid" if form.username.errors else ""), placeholder="Enter username or email") }}
                    {% if form.username.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.username.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group">
                    {{ form.password.label(class="form-label") }}
                    <div class="password-input-group">
                        {{ form.password(class="form-control" + (" is-invalid" if form.password.errors else ""), placeholder="Enter password") }}
                        <button type="button" class="password-toggle" onclick="togglePassword('password')">
                            <i class="bi bi-eye" id="password-toggle-icon"></i>
                        </button>
                    </div>
                    {% if form.password.errors %}
                        <div class="invalid-feedback">
                            {% for error in form.password.errors %}
                                {{ error }}
                            {% endfor %}
                        </div>
                    {% endif %}
                </div>
                
                <div class="form-group form-check">
                    {{ form.remember_me(class="form-check-input") }}
                    {{ form.remember_me.label(class="form-check-label") }}
                </div>
                
                <button type="submit" class="btn btn-primary btn-auth">
                    <i class="bi bi-box-arrow-in-right"></i> Login
                </button>
            </form>
            
            <div class="auth-links">
                <p class="text-center">
                    <a href="{{ url_for('auth.forgot_password') }}" class="auth-link">Forgot your password?</a>
                </p>
                <hr>
                <p class="text-center">
                    Don't have an account? 
                    <a href="{{ url_for('auth.register') }}" class="auth-link-primary">Sign up here</a>
                </p>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script src="{{ url_for('static', filename='js/auth.js') }}"></script>
{% endblock %}

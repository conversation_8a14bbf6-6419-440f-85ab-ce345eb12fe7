<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Registration Form</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css">
    <style>
        body {
            background-color: #f8f9fa;
            padding: 2rem 0;
        }
        .debug-container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 2rem;
            border-radius: 1rem;
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
        }
        .password-strength-meter {
            margin-top: 0.5rem;
            margin-bottom: 1rem;
        }
        .password-strength-bar {
            height: 4px;
            background-color: #e9ecef;
            border-radius: 2px;
            overflow: hidden;
            margin-bottom: 0.5rem;
        }
        .password-strength-fill {
            height: 100%;
            width: 0%;
            transition: all 0.3s ease;
            border-radius: 2px;
        }
        .password-strength-fill.weak {
            background-color: #dc3545;
            width: 25%;
        }
        .password-strength-fill.fair {
            background-color: #fd7e14;
            width: 50%;
        }
        .password-strength-fill.good {
            background-color: #ffc107;
            width: 75%;
        }
        .password-strength-fill.strong {
            background-color: #28a745;
            width: 100%;
        }
        .password-strength-text {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .debug-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 0.5rem;
            padding: 1rem;
            margin-top: 2rem;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 0.875rem;
        }
        .password-input-group {
            position: relative;
        }
        .password-toggle {
            position: absolute;
            right: 0.75rem;
            top: 50%;
            transform: translateY(-50%);
            background: none;
            border: none;
            color: #6c757d;
            cursor: pointer;
            padding: 0.25rem;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h2><i class="bi bi-bug"></i> Debug Registration Form</h2>
        <p class="text-muted">This page helps debug the registration form JavaScript issues.</p>
        
        <form id="debug-registration-form">
            <div class="row">
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="first_name" class="form-label">First Name</label>
                        <input type="text" class="form-control" id="first_name" name="first_name" placeholder="Enter first name">
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="form-group mb-3">
                        <label for="last_name" class="form-label">Last Name</label>
                        <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Enter last name">
                    </div>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label for="username" class="form-label">Username</label>
                <input type="text" class="form-control" id="username" name="username" placeholder="Choose a username">
            </div>
            
            <div class="form-group mb-3">
                <label for="email" class="form-label">Email</label>
                <input type="email" class="form-control" id="email" name="email" placeholder="Enter email address">
            </div>
            
            <div class="form-group mb-3">
                <label for="password" class="form-label">Password</label>
                <div class="password-input-group">
                    <input type="password" class="form-control" id="password" name="password" placeholder="Create password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password')">
                        <i class="bi bi-eye" id="password-toggle-icon"></i>
                    </button>
                </div>
            </div>
            
            <div class="form-group mb-3">
                <label for="password_confirm" class="form-label">Confirm Password</label>
                <div class="password-input-group">
                    <input type="password" class="form-control" id="password_confirm" name="password_confirm" placeholder="Confirm password">
                    <button type="button" class="password-toggle" onclick="togglePassword('password_confirm')">
                        <i class="bi bi-eye" id="password_confirm-toggle-icon"></i>
                    </button>
                </div>
            </div>
            
            <div class="password-strength-meter">
                <div class="password-strength-bar">
                    <div class="password-strength-fill" id="password-strength-fill"></div>
                </div>
                <div class="password-strength-text" id="password-strength-text">Password strength</div>
            </div>
            
            <button type="submit" class="btn btn-primary">
                <i class="bi bi-person-plus"></i> Test Registration
            </button>
        </form>
        
        <div class="debug-log" id="debug-log">
            <strong>Debug Log:</strong><br>
            Page loaded successfully...<br>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Debug logging function
        function debugLog(message) {
            const logElement = document.getElementById('debug-log');
            const timestamp = new Date().toLocaleTimeString();
            logElement.innerHTML += `[${timestamp}] ${message}<br>`;
            logElement.scrollTop = logElement.scrollHeight;
            console.log(`[DEBUG] ${message}`);
        }

        // Password visibility toggle
        function togglePassword(fieldId) {
            debugLog(`togglePassword called for field: ${fieldId}`);
            
            try {
                const passwordField = document.getElementById(fieldId);
                const toggleIcon = document.getElementById(fieldId + '-toggle-icon');
                
                if (!passwordField) {
                    debugLog(`ERROR: Password field '${fieldId}' not found`);
                    return;
                }
                
                if (!toggleIcon) {
                    debugLog(`ERROR: Toggle icon '${fieldId}-toggle-icon' not found`);
                    return;
                }
                
                if (passwordField.type === 'password') {
                    passwordField.type = 'text';
                    toggleIcon.classList.remove('bi-eye');
                    toggleIcon.classList.add('bi-eye-slash');
                    debugLog(`Password field '${fieldId}' visibility toggled to visible`);
                } else {
                    passwordField.type = 'password';
                    toggleIcon.classList.remove('bi-eye-slash');
                    toggleIcon.classList.add('bi-eye');
                    debugLog(`Password field '${fieldId}' visibility toggled to hidden`);
                }
            } catch (error) {
                debugLog(`ERROR in togglePassword: ${error.message}`);
            }
        }

        // Password strength checker
        function checkPasswordStrength(password) {
            debugLog(`checkPasswordStrength called with password length: ${password.length}`);
            
            let score = 0;
            let feedback = [];
            
            if (password.length >= 8) {
                score += 1;
            } else {
                feedback.push('At least 8 characters');
            }
            
            if (/[A-Z]/.test(password)) {
                score += 1;
            } else {
                feedback.push('One uppercase letter');
            }
            
            if (/[a-z]/.test(password)) {
                score += 1;
            } else {
                feedback.push('One lowercase letter');
            }
            
            if (/\d/.test(password)) {
                score += 1;
            } else {
                feedback.push('One number');
            }
            
            if (/[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(password)) {
                score += 1;
            } else {
                feedback.push('One special character');
            }
            
            debugLog(`Password strength score: ${score}/5, feedback: ${feedback.join(', ')}`);
            return { score, feedback };
        }

        // Update password strength meter
        function updatePasswordStrength(password) {
            debugLog(`updatePasswordStrength called`);
            
            try {
                const strengthFill = document.getElementById('password-strength-fill');
                const strengthText = document.getElementById('password-strength-text');
                
                if (!strengthFill) {
                    debugLog('ERROR: password-strength-fill element not found');
                    return;
                }
                
                if (!strengthText) {
                    debugLog('ERROR: password-strength-text element not found');
                    return;
                }
                
                const { score, feedback } = checkPasswordStrength(password);
                
                // Remove all strength classes
                strengthFill.classList.remove('weak', 'fair', 'good', 'strong');
                
                if (password.length === 0) {
                    strengthFill.style.width = '0%';
                    strengthText.textContent = 'Password strength';
                    strengthText.className = 'password-strength-text';
                    debugLog('Password strength reset (empty password)');
                    return;
                }
                
                // Update strength meter based on score
                switch (score) {
                    case 0:
                    case 1:
                        strengthFill.classList.add('weak');
                        strengthText.textContent = 'Weak - Missing: ' + feedback.join(', ');
                        strengthText.className = 'password-strength-text text-danger';
                        debugLog('Password strength: Weak');
                        break;
                    case 2:
                        strengthFill.classList.add('fair');
                        strengthText.textContent = 'Fair - Missing: ' + feedback.join(', ');
                        strengthText.className = 'password-strength-text text-warning';
                        debugLog('Password strength: Fair');
                        break;
                    case 3:
                    case 4:
                        strengthFill.classList.add('good');
                        strengthText.textContent = feedback.length > 0 ? 'Good - Missing: ' + feedback.join(', ') : 'Good password';
                        strengthText.className = 'password-strength-text text-info';
                        debugLog('Password strength: Good');
                        break;
                    case 5:
                        strengthFill.classList.add('strong');
                        strengthText.textContent = 'Strong password';
                        strengthText.className = 'password-strength-text text-success';
                        debugLog('Password strength: Strong');
                        break;
                }
            } catch (error) {
                debugLog(`ERROR in updatePasswordStrength: ${error.message}`);
            }
        }

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('DOM loaded, initializing...');
            
            try {
                // Check if all required elements exist
                const requiredElements = [
                    'password',
                    'password_confirm',
                    'password-strength-fill',
                    'password-strength-text'
                ];
                
                for (const elementId of requiredElements) {
                    const element = document.getElementById(elementId);
                    if (element) {
                        debugLog(`✅ Element '${elementId}' found`);
                    } else {
                        debugLog(`❌ Element '${elementId}' NOT found`);
                    }
                }
                
                // Set up password strength checking
                const passwordField = document.getElementById('password');
                if (passwordField) {
                    passwordField.addEventListener('input', function() {
                        debugLog(`Password input event triggered`);
                        updatePasswordStrength(this.value);
                    });
                    debugLog('Password strength listener added');
                } else {
                    debugLog('ERROR: Could not add password strength listener');
                }
                
                // Set up password confirmation checking
                const confirmField = document.getElementById('password_confirm');
                if (confirmField && passwordField) {
                    confirmField.addEventListener('input', function() {
                        debugLog(`Password confirmation input event triggered`);
                        if (this.value && passwordField.value !== this.value) {
                            this.classList.add('is-invalid');
                            debugLog('Password confirmation mismatch');
                        } else {
                            this.classList.remove('is-invalid');
                            debugLog('Password confirmation matches');
                        }
                    });
                    debugLog('Password confirmation listener added');
                }
                
                // Test initial password strength update
                debugLog('Testing initial password strength update...');
                updatePasswordStrength('');
                
                debugLog('Initialization complete!');
                
            } catch (error) {
                debugLog(`ERROR during initialization: ${error.message}`);
            }
        });

        // Form submission handler
        document.getElementById('debug-registration-form').addEventListener('submit', function(e) {
            e.preventDefault();
            debugLog('Form submission prevented (this is a debug page)');
            
            // Collect form data
            const formData = new FormData(this);
            const data = {};
            for (let [key, value] of formData.entries()) {
                data[key] = value;
            }
            
            debugLog(`Form data: ${JSON.stringify(data, null, 2)}`);
        });
    </script>
</body>
</html>

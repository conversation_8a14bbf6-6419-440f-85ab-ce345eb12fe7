"""
LLM-based news analyzer.

This module provides a news analyzer that uses Large Language Models (LLMs)
to analyze news articles. It supports both single article analysis and batch processing.
"""

import traceback
from utils.logging_config import get_nlp_logger
from datetime import datetime
import json
import argparse
from typing import List, Optional, Dict, Any
from time import sleep

from db.database import DatabaseManager
from apis.llm.base import BaseAPIManager, BudgetLimitExeption, RateLimitExeption
from apis.llm.data_types import CompletionRequest, CompletionStatus
from apis.llm.anthropic import AnthropicManager
from apis.llm.gemini import GeminiManager
from apis.llm.openai import OpenAIManager

from nlp.llm.prompt import PromptManager
from nlp.llm.helpers import TZ, LLMConfig, extract_article_id, generate_custom_id, process_article


# Configure logging
logger = get_nlp_logger(
    __name__, log_file=f'llm/llm_analyzer-{datetime.now().strftime("%Y-%m-%d-%H-%M-%S")}.log')


class LLMAnalyzer:
    """
    Main class for analyzing news articles using LLM APIs.

    Provides both single article analysis and batch processing capabilities
    with proper error handling, caching, and structured output storage.
    """

    def __init__(self, api_name: str, model: str, prompt_type: str, total_budget: float = 0.0,
                 max_tokens: int = 150, max_input: int = 1024,
                 min_input: int = 200, temperature: float = 0.1):
        """Initialize the news analyzer with configuration."""
        self.config = LLMConfig(
            model=model,
            max_tokens=max_tokens,
            max_input=max_input,
            min_input=min_input,
            temperature=temperature
        )
        self.db = DatabaseManager()
        self.prompt_manager = PromptManager()
        # Initialize LLM API
        self.api_name = api_name
        self.prompt_type = prompt_type
        self.llm_api = self._initialize_llm_api(total_budget)

        logger.info("LLMAnalyzer initialized successfully")

    def _initialize_llm_api(self, total_budget: float = 0.0) -> BaseAPIManager:
        """Initialize the appropriate LLM API based on configuration."""
        api_mapping = {
            'openai': OpenAIManager,
            'anthropic': AnthropicManager,
            'gemini': GeminiManager
        }

        api_class = api_mapping.get(self.api_name)
        if not api_class:
            raise ValueError(f"Unsupported API: {self.api_name}")

        current_cost = self.db.llm_api_service.get_total_cost(
            api=self.api_name, prompt_type=self.prompt_type)

        llm_api = api_class(
            total_budget=total_budget,
            total_cost=current_cost
        )

        logger.info(f"Initialized {self.api_name} API with: "
                    f"budget {total_budget}, "
                    f"current cost {current_cost}")
        return llm_api

    def save_completion(self, result_dict: Dict[str, Any]) -> bool:
        """Save completion result to database."""
        try:
            article_id = result_dict['article_id']
            custom_id = generate_custom_id(
                article_id, self.prompt_type, self.api_name)

            db_record = {
                "id": custom_id,
                'article_id': article_id,
                "api": self.api_name,
                "prompt_type": self.prompt_type,
                "model": result_dict['model'],
                "content": result_dict['content'],
                "status": result_dict['status'],
                "cost": result_dict.get('cost'),
                "raw_response": result_dict.get('raw_response'),
                "input_tokens": result_dict.get('input_tokens'),
                "output_tokens": result_dict.get('output_tokens')
            }

            if self.db.llm_api_service.upsert_llm_result(db_record):
                logger.info(
                    f"Successfully saved result: {article_id}")
                return True
            else:
                logger.error(f"Failed to save result: {article_id}")
                return False
        except Exception as e:
            logger.error(f"Error saving result: {e}")
            raise

    def analyze_article(self, article: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze a single article using the specified prompt type."""
        max_retries = 3
        for attempt in range(1, max_retries + 1):
            try:
                # Process article input
                article_input = process_article(
                    article, self.config.min_input, self.config.max_input, logger=logger)
                if not article_input:
                    logger.warning(f"Skipped short article {article['id']}")
                    return {}

                # Get and format prompt
                prompt = self.prompt_manager.get_prompt(self.prompt_type)
                system_prompt = prompt['system_prompt']
                formatted_prompt = prompt['prompt_template'].format(
                    title=article_input['title'],
                    content=article_input['content']
                )

                request = CompletionRequest(
                    max_tokens=self.config.max_tokens,
                    temperature=self.config.temperature,
                    user_prompt=formatted_prompt,
                    system_prompt=system_prompt,
                    model=self.config.model
                )

                # Call LLM API
                completion = self.llm_api.get_completion(request)

                if completion and completion.status == CompletionStatus.SUCCEEDED.value:
                    logger.info(
                        f"Successfully got completion for article {article['id']}")

                    parsed_result = self.prompt_manager.parse_result(
                        completion.content, self.prompt_type)
                    completion_dict = completion.to_dict()
                    completion_dict['content'] = parsed_result
                    completion_dict['article_id'] = article['id']

                    # Save completion result to database
                    self.save_completion(completion_dict)
                    return parsed_result
                else:
                    logger.error(
                        f"Failed to get completion for article {article['id']}")

            except BudgetLimitExeption as e:
                logger.error(f"Budget limit reached: {e}")
                raise
            except RateLimitExeption as e:
                logger.error(f"Rate limit reached: {e}")
            except Exception as e:
                logger.error(
                    f"Error analyzing article {article.get('id', 'unknown')}: {e}")
                raise
            logger.info(f"Retrying in {5 ** attempt} seconds...")
            sleep(5 ** attempt)
        return {}

    def analyze_article_by_url(self, url: str, override: bool = False) -> Dict[str, Any]:
        """Analyze a specific article by URL."""
        try:
            article = self.db.article_service.get_article_by_url(url)
            if not article:
                logger.warning(f"Article not found: {url}")
                return {}

            # Check if already analyzed
            if not override:
                existing_result = self.db.llm_api_service.get_result_by_article_id(
                    article['id'])
                if existing_result:
                    logger.info(f"Returning existing result for {url}")
                    return existing_result['content']

            return self.analyze_article(article)

        except Exception as e:
            logger.error(f"Error analyzing article by URL {url}: {e}")

        return {}

    def analyze_articles(
            self,
            start_date: datetime | str,
            end_date: Optional[datetime | str] = None,
            override: bool = False,
            max_articles: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """Analyze a list of articles."""
        if isinstance(start_date, str):
            start_date = datetime.strptime(
                start_date, "%Y-%m-%d").astimezone(TZ)
        if isinstance(end_date, str):
            end_date = datetime.strptime(end_date, "%Y-%m-%d").astimezone(TZ)
        if override:
            articles = self.db.article_service.get_articles(
                start_date, end_date, limit=max_articles)
        else:
            articles = self.db.article_service.get_articles_for_llm_batch(
                # Exclude already succeeded articles with the api and prompt type
                prompt_type=self.prompt_type,
                excluded_statuses=[
                    CompletionStatus.SUCCEEDED.value,
                    CompletionStatus.IN_PROGRESS.value
                ],
                start_date=start_date,
                end_date=end_date,
                limit=max_articles,
                min_words=self.config.min_input
            )
        logger.info(f"Found {len(articles)} articles to analyze")

        results = []
        for article in articles:
            result = self.analyze_article(article)
            if self.db.article_service.add_article_metadata(
                    article['id'], {self.prompt_type: result}):
                logger.info(
                    f"Successfully saved in article metadata: {article['id']} ")
            else:
                logger.error(
                    f"Failed to save in article metadata: {article['id']}")

            results.append(result)
        return results


def main():
    """Main function to run the analyzer from command line."""
    parser = argparse.ArgumentParser(
        description='Analyze news articles using LLM')

    # Basic options
    parser.add_argument('-u', '--url', help='URL of article to analyze')
    parser.add_argument('-a', '--api', default='gemini', help='LLM API name')
    parser.add_argument(
        '-m', '--model', default='gemini-2.0-flash-001', help='LLM model to use')
    parser.add_argument('-t', '--prompt-type',
                        default='influence_tagging', help='Type of analysis to perform')
    parser.add_argument('-s', '--start-date', help='Start date (YYYY-MM-DD)')
    parser.add_argument('-e', '--end-date', help='End date (YYYY-MM-DD)')

    # Resource limits
    parser.add_argument('-b', '--budget', type=float, default=5.0,
                        help='Maximum budget for API calls')
    parser.add_argument('--max-articles', type=int, default=1000,
                        help='Maximum articles to process per cycle')
    parser.add_argument('--max-input', type=int, default=1024,
                        help='Maximum input words')
    parser.add_argument('--max-tokens', type=int, default=150,
                        help='Maximum output tokens')
    parser.add_argument('--min-input', type=int, default=100,
                        help='Minimum input words')
    parser.add_argument('--temperature', type=float, default=0.1,
                        help='Temperature for LLM output')
    parser.add_argument('--override', action='store_true',
                        help='Override existing results')

    args = parser.parse_args()

    try:
        # Initialize analyzer
        analyzer = LLMAnalyzer(args.api, args.model, args.prompt_type,
                               args.budget, args.max_tokens, args.max_input, args.min_input, args.temperature)

        if args.url:
            result = analyzer.analyze_article_by_url(args.url, args.override)
            if result:
                print(json.dumps(result, indent=2))
            else:
                print(f"Analysis failed for URL: {args.url}")
        elif args.start_date:
            analyzer.analyze_articles(
                args.start_date, args.end_date, args.override, args.max_articles)
    except Exception as e:
        logger.error(f"Application error: {e}")


if __name__ == '__main__':
    main()
